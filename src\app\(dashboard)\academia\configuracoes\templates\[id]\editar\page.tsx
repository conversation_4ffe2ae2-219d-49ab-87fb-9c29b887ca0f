/**
 * Página para edição de template de notificação
 * Interface dedicada com preview ampliado
 */

'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { useTemplateManagement } from '@/services/notifications';
import { TemplateEditorPage } from '@/components/notifications/templates/template-editor-page';
import type {
  NotificationTemplate,
  UpdateTemplateData
} from '@/services/notifications/types/notification-types';

interface EditarTemplatePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditarTemplatePage({ params }: EditarTemplatePageProps) {
  const resolvedParams = use(params);
  const router = useRouter();
  const [saving, setSaving] = useState(false);
  const [template, setTemplate] = useState<NotificationTemplate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { updateTemplateMutation, templates, loadTemplates } = useTemplateManagement();

  // Carregar templates
  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  // Buscar template específico quando templates carregarem
  useEffect(() => {
    if (templates.length > 0) {
      const foundTemplate = templates.find(t => t.id === resolvedParams.id);
      if (foundTemplate) {
        setTemplate(foundTemplate);
        setLoading(false);
      } else {
        setError('Template não encontrado');
        setLoading(false);
      }
    }
  }, [templates, resolvedParams.id]);

  const handleSave = async (data: UpdateTemplateData) => {
    if (!template) return;

    setSaving(true);
    try {
      await updateTemplateMutation.mutateAsync({ id: template.id, data });
      toast.success('Template atualizado com sucesso!');
      router.push('/academia/configuracoes/templates');
    } catch (error) {
      toast.error('Erro ao atualizar template');
      console.error('Erro ao atualizar template:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.push('/academia/configuracoes/templates');
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <Loader2 className="w-6 h-6 animate-spin mr-2" />
              Carregando template...
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-destructive mb-4">{error || 'Template não encontrado'}</p>
              <Button onClick={handleCancel}>Voltar para Templates</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Voltar
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-foreground">Editar Template</h1>
          <p className="text-muted-foreground">
            Editando: {template.name}
          </p>
        </div>
      </div>

      {/* Editor */}
      <TemplateEditorPage
        template={template}
        onSave={handleSave}
        onCancel={handleCancel}
        saving={saving}
        isEditing
      />
    </div>
  );
}
