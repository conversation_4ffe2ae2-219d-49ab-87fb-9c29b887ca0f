/**
 * Editor de templates em página completa
 * Versão otimizada com preview ampliado para melhor experiência do usuário
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  useTemplatePreview,
  useTemplateValidationDebounced
} from '@/hooks/notifications/use-template-preview';
import { useTemplateVariables } from '@/hooks/notifications/use-template-variables';
import { EmailPreview } from './email-preview';
import { VariableInserter } from './variable-inserter';
import type { 
  NotificationTemplate, 
  NotificationType, 
  NotificationChannel,
  CreateTemplateData,
  UpdateTemplateData 
} from '@/services/notifications/types/notification-types';

interface TemplateEditorPageCreateProps {
  template?: never;
  onSave: (data: CreateTemplateData) => Promise<void>;
  onCancel: () => void;
  saving?: boolean;
  isEditing?: false;
}

interface TemplateEditorPageEditProps {
  template: NotificationTemplate;
  onSave: (data: UpdateTemplateData) => Promise<void>;
  onCancel: () => void;
  saving?: boolean;
  isEditing: true;
}

type TemplateEditorPageProps = TemplateEditorPageCreateProps | TemplateEditorPageEditProps;

export function TemplateEditorPage({ 
  template, 
  onSave, 
  onCancel, 
  saving = false,
  isEditing = false 
}: TemplateEditorPageProps) {
  // Estado do formulário
  const [formData, setFormData] = useState({
    name: template?.name || '',
    type: template?.type || 'payment' as NotificationType,
    channel: template?.channel || 'email' as NotificationChannel,
    subject_template: template?.subject_template || '',
    body_template: template?.body_template || ''
  });

  // Hooks para funcionalidades
  const { variables, exampleVariables, loadVariables } = useTemplateVariables();
  const { renderTemplate } = useTemplatePreview();
  const validation = useTemplateValidationDebounced(
    formData.body_template, 
    formData.type, 
    500
  );

  // Carregar variáveis quando tipo mudar
  useEffect(() => {
    loadVariables(formData.type);
  }, [formData.type, loadVariables]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    try {
      if (isEditing) {
        const updateData: UpdateTemplateData = {
          name: formData.name,
          subject_template: formData.subject_template,
          body_template: formData.body_template
        };
        await (onSave as (data: UpdateTemplateData) => Promise<void>)(updateData);
      } else {
        const createData: CreateTemplateData = {
          name: formData.name,
          type: formData.type,
          channel: formData.channel,
          subject_template: formData.subject_template,
          body_template: formData.body_template
        };
        await (onSave as (data: CreateTemplateData) => Promise<void>)(createData);
      }
    } catch (error) {
      console.error('Erro ao salvar template:', error);
    }
  };

  const insertVariable = (variableKey: string) => {
    const textarea = document.getElementById('body-template') as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const after = text.substring(end);
      const newText = before + `{{${variableKey}}}` + after;

      handleInputChange('body_template', newText);

      // Reposicionar cursor
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(
          start + variableKey.length + 4,
          start + variableKey.length + 4
        );
      }, 0);
    }
  };

  return (
    <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
      {/* Editor - Coluna da esquerda */}
      <div className="xl:col-span-1 space-y-6 overflow-y-auto">
        <Card className="h-fit">
          <CardHeader>
            <CardTitle>
              {isEditing ? 'Configurações do Template' : 'Novo Template'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Nome */}
            <div>
              <Label htmlFor="name">Nome do Template</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Ex: Lembrete de Pagamento"
              />
            </div>

            {/* Tipo e Canal (apenas para criação) */}
            {!isEditing && (
              <>
                <div>
                  <Label htmlFor="type">Tipo</Label>
                  <Select 
                    value={formData.type} 
                    onValueChange={(value) => handleInputChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="payment">Pagamento</SelectItem>
                      <SelectItem value="class">Aula</SelectItem>
                      <SelectItem value="enrollment">Matrícula</SelectItem>
                      <SelectItem value="event">Evento</SelectItem>
                      <SelectItem value="system">Sistema</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="channel">Canal</Label>
                  <Select 
                    value={formData.channel} 
                    onValueChange={(value) => handleInputChange('channel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="whatsapp">WhatsApp</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}

            {/* Assunto (apenas para email) */}
            {formData.channel === 'email' && (
              <div>
                <Label htmlFor="subject">Assunto (Email)</Label>
                <Input
                  id="subject"
                  value={formData.subject_template}
                  onChange={(e) => handleInputChange('subject_template', e.target.value)}
                  placeholder="Ex: {{academyName}} - Lembrete de Pagamento"
                />
              </div>
            )}

            <Separator />

            {/* Corpo do template */}
            <div>
              <Label htmlFor="body-template">Conteúdo do Template</Label>
              <RichTextEditor
                content={formData.body_template}
                onChange={(content) => handleInputChange('body_template', content)}
                placeholder="Digite o conteúdo do template aqui..."
                className="mt-2"
                variables={variables.map(variable => ({
                  key: variable.variable_key,
                  label: variable.variable_name,
                  description: variable.description
                }))}
                onInsertVariable={(variableKey) => {
                  console.log('Variável inserida:', variableKey);
                }}
              />
            </div>

            {/* Validação */}
            {validation && validation.errors.length > 0 && (
              <Alert variant="destructive">
                <AlertDescription>
                  <strong>Erros:</strong>
                  <ul className="list-disc list-inside mt-1">
                    {validation.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Ações */}
            <div className="flex gap-2 pt-4">
              <Button 
                onClick={handleSave} 
                disabled={saving || !validation?.isValid}
                className="flex-1"
              >
                {saving ? 'Salvando...' : 'Salvar Template'}
              </Button>
              <Button variant="outline" onClick={onCancel}>
                Cancelar
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Preview e Variáveis - Colunas da direita */}
      <div className="xl:col-span-2 space-y-6">
        <Tabs defaultValue="preview" className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="variables">Variáveis</TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="flex-1 mt-6 h-full">
            {formData.channel === 'email' ? (
              <EmailPreview
                subject={formData.subject_template}
                body={formData.body_template}
                variables={exampleVariables}
                className="h-full flex flex-col"
              />
            ) : (
              <Card className="h-full flex flex-col">
                <CardHeader className="flex-shrink-0">
                  <CardTitle>Preview - {formData.channel === 'whatsapp' ? 'WhatsApp' : 'Notificação'}</CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col">
                  <div className="bg-muted/50 dark:bg-muted/20 p-6 rounded-lg border border-border flex-1 overflow-y-auto">
                    <div
                      className="whitespace-pre-wrap text-foreground leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: renderTemplate(formData.body_template, exampleVariables)
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="variables" className="flex-1 mt-6">
            <VariableInserter
              variables={variables}
              exampleVariables={exampleVariables}
              onInsertVariable={insertVariable}
              className="h-full"
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
